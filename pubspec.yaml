name: rapsap
description: A new Flutter project.

publish_to: "none" 

version: 1.1.4+25

environment:
  sdk: ">=3.4.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter


 # UI / Animation Packages
  avatar_glow: ^3.0.0
  flutter_bounceable: ^1.2.0
  cached_network_image: ^3.4.1
  carousel_slider: ^4.2.1
  marquee: ^2.2.3 
  dotted_border: ^2.1.0
  shimmer: ^3.0.0
  rive: ^0.13.13
  confetti: ^0.7.0
  group_button: ^5.3.4
  page_view_dot_indicator: ^2.0.2
  loading_indicator: ^3.1.1
  timeline_tile: ^2.0.0
  staggered_grid_view_flutter: ^0.0.4

  # Firebase Packages (Latest Compatible Versions for Flutter 3.22.3)
  firebase_core: ^3.15.2
  firebase_auth: ^5.7.0
  firebase_analytics: ^11.6.0
  firebase_crashlytics: ^4.3.10
  firebase_messaging: ^15.2.10
  firebase_performance: ^0.10.1+10
  firebase_dynamic_links: ^6.1.10  # Note: This package is discontinued but still functional
  firebase_remote_config: ^5.5.0
  firebase_in_app_messaging: ^0.8.1+10

  # Device / Platform Integration
  flutter_displaymode: ^0.6.0
  flutter_local_notifications: ^17.2.3
  flutter_svg: ^2.0.10+1
  geocoding: ^3.0.0
  geolocator: ^13.0.1
  google_fonts: ^6.2.1
  google_maps_flutter: ^2.9.0
  google_sign_in: ^6.2.1
  image_picker: ^1.1.2
  intl: ^0.19.0
  intl_phone_number_input: ^0.7.4
  modal_bottom_sheet: ^3.0.0
  package_info_plus: ^8.0.2
  pinput: ^5.0.0
  razorpay_flutter: ^1.3.7
  scratcher: ^2.5.0
  url_launcher: ^6.3.1
  connectivity_plus: ^6.0.5
  # mobile_number_picker: ^0.0.1+5
  intl_phone_field: ^3.2.0
  smart_auth: ^1.1.1
  upgrader: ^10.3.0
  http: ^1.2.2

  # State Management & Storage
  get: ^4.6.6
  get_storage: ^2.1.1

dependency_overrides:
  fading_edge_scrollview: ^4.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0
  flutter_launcher_icons: ^0.14.1

    
flutter_icons:
  image_path: "assets/rapsap-icon.png"
  android: true
  ios: true
  remove_alpha_ios: true

flutter:
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/images/offerimages/
    - assets/svg/
    - assets/rive/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.
  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages
  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

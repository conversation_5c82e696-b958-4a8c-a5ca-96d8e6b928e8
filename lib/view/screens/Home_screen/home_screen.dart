import 'dart:math';

import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:rapsap/controllers/categorycontroller.dart';
import 'package:rapsap/controllers/ordercontroller.dart';
import 'package:rapsap/controllers/root_view_controller.dart';
import 'package:rapsap/controllers/wishlistcontroller.dart';
import 'package:rapsap/view/screens/Home_screen/widgets/shimmer.dart';
import 'package:rapsap/view/screens/mapscreen/setlocation.dart';
// import 'package:rapsap/model/homepageproducts/home_page_products/datum.dart';
// import 'package:rapsap/model/rewards/rewardsmode.dart';
import 'package:rapsap/view/screens/rewards/rewardlistscreen.dart';
import 'package:rapsap/view/widgets/buttonanimation.dart';
import 'package:rapsap/view/widgets/custom.dart';
import 'package:rapsap/view/widgets/keyboardhider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';

import 'package:page_view_dot_indicator/page_view_dot_indicator.dart';
import 'package:rapsap/controllers/accountscontroller.dart';
import 'package:rapsap/view/widgets/commons.dart';
import 'package:rapsap/controllers/cartcontrolller.dart';
import 'package:rapsap/controllers/home_view_controller.dart';
import 'package:rapsap/controllers/product_view_controller.dart';
import 'package:rapsap/main.dart';
import 'package:rapsap/model/CategoryModel/data.dart';
// import 'package:rapsap/model/category_product_model/category_products.dart';
import 'package:rapsap/utils/utils.dart';
import 'package:rapsap/view/screens/category_pages/category_page.dart';
import 'package:rapsap/view/screens/mapscreen/mappage.dart';
import 'package:rapsap/view/screens/product_screen/product_screen.dart';
import 'package:rapsap/view/screens/searchscreen/search.dart';
// import 'package:rapsap/view/screens/wishlist/wishlistscreen.dart';
import 'package:rapsap/view/widgets/custom_shimmer.dart';
import 'package:shimmer/shimmer.dart';
// import '../../../controllers/ordercontroller.dart';
import '../../../model/homepageproducts/home_page_products/product.dart';
import '../../../services/databaseHelper.dart';
import '../../../services/firebaseservices.dart';
import '../../widgets/animationviewcart.dart';
import '../cart_screen/cart_screen.dart';

// Updated HomeScreen
class HomeScreen extends StatelessWidget {
  HomeScreen({super.key});

  final _usercontroller = Get.find<UserController>();
  final controller = Get.find<HomeViewController>();
  final ProductViewController productViewController =
      Get.find<ProductViewController>();
  final CartController cartController = Get.find<CartController>();
  final OrderController orderController = Get.find<OrderController>();

  @override
  Widget build(BuildContext context) {
    if (storage.read('locationAddress') != null) {
      _usercontroller.locationaddress.value = storage.read('locationAddress');
    }

    DatabaseHelper.instance.getGroceries();
    Get.isRegistered<AccountController>()
        ? Get.find<AccountController>()
        : Get.put(AccountController());

    return GetBuilder<HomeViewController>(
      id: "home",
      builder: (controller) {
        // Show error state if there's an error
        if (controller.hasError) {
          return Scaffold(
            backgroundColor: Colors.white,
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 64, color: Colors.red),
                  const SizedBox(height: 16),
                  const Text(
                    'Something went wrong',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 32),
                    child: Text(
                      controller.errorMessage,
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () => controller.getHomePageViews(),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
          );
        }

        // Show loading state
        if (controller.isLoading) {
          return const ShimmerHomeScreen();
        }

        // Show content if minimum required data is available
        if (controller.categoryModel.isNotEmpty &&
            controller.categoryModel.first != null &&
            controller.bannerItems.isNotEmpty) {
          return HomeScreenWidget();
        }

        // Fallback - show empty state
        return Scaffold(
          backgroundColor: Colors.white,
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.inbox, size: 64, color: Colors.grey),
                const SizedBox(height: 16),
                const Text(
                  'No data available',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () => controller.getHomePageViews(),
                  child: const Text('Refresh'),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class HomeScreenWidget extends StatelessWidget {
  HomeScreenWidget({super.key});

  final _usercontroller = Get.find<UserController>();

  final CarouselController carouselController = CarouselController();

  final AccountController accountController = Get.find();

  final controller = Get.find<HomeViewController>();

  final ProductViewController productViewController =
      Get.find<ProductViewController>();

  final CartController cartController = Get.find<CartController>();

  final Wishlistcontroller wishlistcontroller = Get.find();

  final RootViewController rootViewController = Get.find();

  // final ScrollController scrollController = ScrollController();

  caontrollerupdta() {
    controller.pinned = false;
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        SystemNavigator.pop();
        return false;
      },
      child: RefreshIndicator(
        displacement: 150,
        onRefresh: (() {
          debugPrint("StoreID=${storage.read('storeID').runtimeType}");
          wishlistcontroller.getwishlistitems();

          return controller.getHomePageViews();
        }),
        child: Scaffold(
          backgroundColor: kwhite,
          body: KeyboardHider(
            child: Stack(
              children: [
                CustomScrollView(
                  // primary: false,
                  scrollBehavior: const CupertinoScrollBehavior(),
                  physics: const BouncingScrollPhysics(),
                  slivers: [
                    GetBuilder<HomeViewController>(
                      id: "scroll",
                      builder: (_) {
                        return SliverAppBar(
                          pinned: true,
                          // stretch: true,
                          snap: false,
                          floating: true,
                          automaticallyImplyLeading: false,
                          elevation: 0,
                          actions: [
                            Padding(
                              padding: const EdgeInsets.only(left: 30),
                              child: GestureDetector(
                                onTap: (() {
                                  if (_usercontroller.loginstatus.value) {
                                    Get.to(() => MyRewardsScreen());
                                  } else {
                                    Get.to(() => const LoginScreen());
                                  }
                                }),
                                child: Obx(() => Padding(
                                      padding: const EdgeInsets.only(
                                          right: 8, bottom: 7),
                                      child: controller.rewards.value == true
                                          ? Bounceable(
                                              onTap: () {},
                                              child: getSvgIcon(
                                                  "assets/svg/gift.svg"))
                                          : getSvgIcon("assets/svg/gift.svg"),
                                    )),
                              ),
                            ),
                            SizedBox(
                              width: Utils.getScreenWidthByPercentage(1),
                            ),
                            const SizedBox(width: defaultpadding),
                          ],
                          backgroundColor: kwhite,
                          title: InkWell(
                            onTap: () {
                              Get.to(() => MapPage());
                            },
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                getSvgIcon("assets/svg/location.svg"),
                                SizedBox(
                                  width: Utils.getScreenWidthByPercentage(1),
                                ),
                                Flexible(
                                  flex: 2,
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        storage.read('place') ?? '',
                                        overflow: TextOverflow.fade,
                                        style: const TextStyle(
                                            fontWeight: FontWeight.w700,
                                            fontSize: 15,
                                            color: kblack),
                                      ),
                                      Text(storage.read('tempPincode') ?? "",
                                          overflow: TextOverflow.clip,
                                          style: const TextStyle(
                                              fontWeight: FontWeight.w400,
                                              fontSize: 12,
                                              color: kblack))
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          bottom: PreferredSize(
                            preferredSize: const Size.fromHeight(70),
                            child: ClipRRect(
                              child: AppBar(
                                toolbarHeight: 70,
                                automaticallyImplyLeading: false,
                                backgroundColor:
                                    _.pinned ? Colors.transparent : kwhite,
                                elevation: 0,

                                titleSpacing: 0,
                                // scrolledUnderElevation: 2,
                                flexibleSpace: Container(
                                  color: kwhite,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: defaultpadding, vertical: 12),
                                  width: double.infinity,
                                  child: _customSearchBar(),
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                    SliverList(
                      delegate: SliverChildBuilderDelegate(
                        (context, index) => Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: defaultpadding, vertical: 20),
                          child: Stack(
                            children: [
                              CarouselSlider.builder(
                                carouselController: carouselController,
                                itemCount: controller.bannerItems.length,
                                itemBuilder: (BuildContext context,
                                    int itemIndex, int pageViewIndex) {
                                  if (controller.bannerItems.isEmpty) {
                                    return const SizedBox();
                                  }
                                  double scale = 1;
                                  return StatefulBuilder(
                                      builder: (context, setState) {
                                    return GestureDetector(
                                      onTapDown: ((details) {
                                        setState(
                                          () {
                                            scale = 0.95;
                                          },
                                        );
                                        HapticFeedback.heavyImpact();
                                      }),
                                      onTapUp: ((details) {
                                        setState(
                                          () {
                                            scale = 1;
                                          },
                                        );
                                      }),
                                      onTapCancel: (() {
                                        setState(
                                          () {
                                            scale = 1;
                                          },
                                        );
                                      }),
                                      onTap: () {
                                        if (controller.bannerItems[itemIndex]
                                                    .productId !=
                                                null &&
                                            controller.bannerItems[itemIndex]
                                                    .type ==
                                                "product") {
                                          // Get.to(() => ProductScreen(
                                          //       productId: controller
                                          //           .bannerItems[itemIndex]
                                          //           .productId!,
                                          //     ));
                                        }
                                      },
                                      child: InkWell(
                                        child: ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(2),
                                          child: Transform.scale(
                                            scale: scale,
                                            child: controller
                                                        .bannerItems[itemIndex]
                                                        .bannerImage ==
                                                    null
                                                ? controller.bannerimage
                                                : CachedNetworkImage(
                                                    height: 180,
                                                    width: double.infinity,
                                                    fit: BoxFit.cover,
                                                    imageUrl: controller
                                                        .bannerItems[itemIndex]
                                                        .bannerImage!,
                                                    placeholder: (context,
                                                            url) =>
                                                        Center(
                                                            child:
                                                                CustomAppShimmer(
                                                                    child:
                                                                        Container(
                                                      color: Colors.grey,
                                                      height: 180,
                                                    ))),
                                                    errorWidget: (context, url,
                                                            error) =>
                                                        controller.bannerimage,
                                                  ),
                                          ),
                                        ),
                                      ),
                                    );
                                  });
                                },
                                options: CarouselOptions(
                                  scrollPhysics: const BouncingScrollPhysics(),
                                  // autoPlayCurve: Curves.linear,
                                  enlargeCenterPage: true,
                                  onPageChanged: ((index, reason) {
                                    controller.selectedpageitem = index;
                                    controller.update();
                                  }),

                                  initialPage: 0,
                                  height: 180,
                                  viewportFraction: 1,
                                  padEnds: true,
                                  clipBehavior: Clip.antiAlias,
                                  autoPlay: true,
                                ),
                              ),
                              controller.bannerItems.length > 1
                                  ? Positioned(
                                      left: -25,
                                      bottom: 20,
                                      child: GetBuilder<HomeViewController>(
                                        builder: (controller) {
                                          return PageViewDotIndicator(
                                            unselectedSize: const Size(8, 6),
                                            size: const Size(10, 8),
                                            currentItem:
                                                controller.selectedpageitem,
                                            count:
                                                controller.bannerItems.length,
                                            unselectedColor:
                                                const Color(0xff556f80),
                                            selectedColor: Colors.white,
                                          );
                                        },
                                      ),
                                    )
                                  : const SizedBox()
                            ],
                          ),
                        ),
                        childCount: 1,
                      ),
                    ),
                    SliverFixedExtentList(
                      delegate: SliverChildBuilderDelegate(
                        (context, index) => Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: defaultpadding),
                          child: Text(
                            "Shop by Category",
                            style: GoogleFonts.dmSans(
                              fontSize: 22,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                        ),
                        childCount: 1,
                      ),
                      itemExtent: 35,
                    ),
                    SliverPadding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: defaultpadding, vertical: 10),
                      sliver: SliverGrid(
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 3,
                            mainAxisSpacing: 10,
                            crossAxisSpacing: 10,
                            childAspectRatio: 0.80,
                          ),
                          delegate: SliverChildBuilderDelegate(
                            (BuildContext context, int index) {
                              if (!controller.isCategoryLoading) {
                                final data =
                                    controller.categoryModel.elementAt(index);
                                return _categoryItemWidget(data!);
                              } else {
                                return GetBuilder<HomeViewController>(
                                  builder: (controller) =>
                                      const CategoryCardShimmer(),
                                );
                              }
                            },
                            childCount: !controller.isCategoryLoading
                                ? controller.categoryModel.length
                                : 5,
                          )),
                    ),
                    ...List.generate(controller.homepageDatalist.length,
                        (cindex) {
                      return SliverToBoxAdapter(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 10),
                          child: Container(
                            color: kblack.withOpacity(0.06),
                            child: Column(
                              children: [
                                hgap(20),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Expanded(
                                      child: Text(
                                        "${controller.homepageDatalist[cindex].category.toString().capitalize}",
                                        style: GoogleFonts.dmSans(
                                          fontSize: 22,
                                          letterSpacing: -1.1,
                                          color: kblack,
                                          fontWeight: FontWeight.w700,
                                        ),
                                      ).paddingOnly(right: 15),
                                    ),
                                    InkWell(
                                      onTap: () => Get.to(() => CategoryScreen(
                                          categoryname: controller
                                              .homepageDatalist[cindex].category
                                              .toString()
                                              .capitalize!,
                                          categoryid: controller
                                              .homepageDatalist[cindex]
                                              .categoryId!)),
                                      child: Container(
                                        width: 80,
                                        height: 30,
                                        decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(4),
                                            color: kblack),
                                        child: Center(
                                          child: Text(
                                            "View All",
                                            style: GoogleFonts.dmSans(
                                              fontSize: 14,
                                              color: kwhite,
                                              fontWeight: FontWeight.w700,
                                            ),
                                          ),
                                        ),
                                      ),
                                    )
                                  ],
                                ).paddingSymmetric(horizontal: defaultpadding),
                                kheight20,
                                SizedBox(
                                    height: 260,
                                    child: controller
                                            .homepageDatalist.isNotEmpty
                                        ? Column(
                                            children: [
                                              Flexible(
                                                child: ListView.separated(
                                                  physics:
                                                      const BouncingScrollPhysics(),
                                                  shrinkWrap: true,
                                                  scrollDirection:
                                                      Axis.horizontal,
                                                  itemCount: controller
                                                      .homepageDatalist[cindex]
                                                      .products!
                                                      .length,
                                                  itemBuilder:
                                                      (context, index) {
                                                    final model = controller
                                                        .homepageDatalist[
                                                            cindex]
                                                        .products![index];
                                                    return _customCardWidget(
                                                        model, index);
                                                  },
                                                  separatorBuilder:
                                                      (context, index) =>
                                                          const SizedBox(
                                                    width: 10,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          )
                                        : ListView.separated(
                                            physics:
                                                const BouncingScrollPhysics(),
                                            scrollDirection: Axis.horizontal,
                                            itemCount: 4,
                                            itemBuilder: (context, index) {
                                              return _productCardShimmer(index);
                                            },
                                            separatorBuilder:
                                                (context, index) =>
                                                    const SizedBox(
                                              width: 8,
                                            ),
                                          )),
                                hgap(10)
                              ],
                            ),
                          ),
                        ),
                      );
                    }),
                    const SliverPadding(padding: EdgeInsets.all(50))
                  ],
                ),
                const AnimatedCartView()
              ],
            ),
          ),
        ),
      ),
    );
  }

  _productCardShimmer(index) {
    return Row(
      children: [
        kwidth20,
        SizedBox(
          child: Shimmer.fromColors(
            baseColor: Colors.grey[200]!,
            highlightColor: Colors.grey[100]!,
            child: Container(
              height: 270,
              width: 150,
              color: Colors.grey,
            ),
          ),
        ),
      ],
    );
  }

  _customCardWidget(Product? model, index) {
    // print("model=${model!.toJson()}"),;
    var discountVal = model!.mrp == null
        ? 0
        : ((100 *
                    (double.parse(
                            model.mrp == null ? '0' : model.mrp.toString()) -
                        double.parse(model.price == null
                            ? '0'
                            : model.price.toString()))) /
                double.parse(model.mrp == null ? '0' : model.mrp.toString()))
            .floorToDouble();

    return Row(
      children: [
        index == 0
            ? const SizedBox(
                width: 16,
              )
            : const SizedBox(),
        Column(children: [
          SizedBox(
            width: 140,
            height: 245,
            child: Stack(
              children: [
                InkWell(
                  onTap: () {
                    showBarModalBottomSheet(
                        barrierColor: kblack.withOpacity(0.2),
                        context: Get.context!,
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10)),
                        builder: ((context) {
                          return SizedBox(
                            height: Get.height * 0.8,
                            child: ProductScreen(
                              productId: model.productId!,
                              variantId: model.variantId,
                            ),
                          );
                        }));
                  },
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(2),
                    child: Container(
                      height: 232,
                      decoration: const BoxDecoration(
                        color: Colors.white,
                      ),
                      child: Stack(
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(10),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                const SizedBox(height: 2),
                                SizedBox(
                                  height: 110,
                                  child: model.images!.isEmpty
                                      ? Image.asset(
                                          "assets/images/error-image.png")
                                      : CachedNetworkImage(
                                          imageUrl: "${model.images}",
                                          fit: BoxFit.cover,
                                          errorWidget: (context, error,
                                                  stackTrace) =>
                                              Image.asset(
                                                  "assets/images/error-image.png"),
                                        ),
                                ),
                                const SizedBox(height: 5),
                                SizedBox(
                                  height: 36,
                                  child: Align(
                                    alignment: Alignment.centerLeft,
                                    child: Text(
                                      "${model.product.toString().capitalizeFirst}",
                                      overflow: TextOverflow.ellipsis,
                                      maxLines: 2,
                                      textAlign: TextAlign.start,
                                      style: GoogleFonts.dmSans(
                                        height: 1,
                                        fontSize: 16,
                                        color: Colors.black,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                ),
                                Align(
                                  alignment: Alignment.topLeft,
                                  child: Text(
                                    model.weight != null
                                        ? getweight(model.weight.toString())
                                        : "",
                                    style: GoogleFonts.dmSans(
                                        fontSize: 12,
                                        color: Colors.grey,
                                        fontWeight: FontWeight.w400,
                                        letterSpacing: -0.1),
                                  ),
                                ),
                                const SizedBox(height: 7),
                                Align(
                                  alignment: Alignment.topLeft,
                                  child: Row(
                                    children: [
                                      FittedBox(
                                        child: Text(
                                          "₹${double.parse((model.price ?? '').toString()).round()}",
                                          style: GoogleFonts.dmSans(
                                            fontSize: 16,
                                            color: Colors.black,
                                            fontWeight: FontWeight.w700,
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 5),
                                      Text(
                                        model.price == model.mrp ||
                                                model.mrp == "0.00"
                                            ? ""
                                            : "₹${double.parse((model.mrp ?? '').toString()).round()}",
                                        style: GoogleFonts.dmSans(
                                          fontSize: 12,
                                          color: kblack.withOpacity(0.5),
                                          fontWeight: FontWeight.w400,
                                          decoration:
                                              TextDecoration.lineThrough,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 8),
                              ],
                            ),
                          ),
                          discountVal == 0
                              ? const SizedBox(
                                  width: 32,
                                  height: 44,
                                )
                              : Container(
                                  child: Align(
                                      alignment: Alignment.topRight,
                                      child: SizedBox(
                                        width: 32,
                                        height: 44,
                                        child: Stack(
                                          alignment: Alignment.topRight,
                                          children: [
                                            SvgPicture.asset(
                                              'assets/svg/offertag.svg',
                                              color: const Color(0xffFF5454),
                                            ),
                                            Center(
                                              child: Text(
                                                "${discountVal.round()}% \n OFF",
                                                style: const TextStyle(
                                                    color: Colors.white,
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.w700,
                                                    letterSpacing: -0.5),
                                                textAlign: TextAlign.center,
                                              ).paddingOnly(bottom: 10),
                                            ),
                                          ],
                                        ),
                                      )),
                                ),
                        ],
                      ),
                    ),
                  ),
                ),
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: StatefulBuilder(builder: (context, setState) {
                    // DatabaseHelper.instance.getGroceries();

                    return GetBuilder<CartController>(
                        builder: (cartController) {
                      return FutureBuilder<int>(
                        future: DatabaseHelper.instance.getQty(ShopingCart(
                            variantID: model.variantId!,
                            productID: model.productId!)),
                        builder: (context, snapshot) {
                          return snapshot.data == 0
                              ? InkWell(
                                  onTap: (() async {
                                    await DatabaseHelper.instance
                                        .addUpdate(ShopingCart(
                                      mrp: double.parse(model.mrp.toString()),
                                      name: model.product,
                                      qty: 1,
                                      productID: model.productId!,
                                      price:
                                          double.parse(model.price.toString()),
                                      variantID: model.variantId!,
                                      imageURL: model.images ?? '',
                                      weight:
                                          double.parse(model.weight.toString()),
                                    ));

                                    print('added to cart');
                                    final eventItem = AnalyticsEventItem(
                                      itemId: model.productId.toString(),
                                      itemName: model.product,
                                      itemCategory: model.category,
                                      itemVariant: model.variantName,
                                      price:
                                          double.parse(model.price.toString()),
                                      quantity: 1,
                                    );
                                    await FirebaseService.firebaseAnalytics!
                                        .logAddToCart(items: [eventItem]);

                                    setState(() {
                                      // update = 1;
                                    });
                                  }),
                                  child: Center(
                                      child: Container(
                                          height: 34,
                                          width: 34,
                                          alignment: Alignment.center,
                                          decoration: BoxDecoration(
                                              color: kblack,
                                              borderRadius:
                                                  BorderRadius.circular(2)),
                                          child: const Icon(
                                            Icons.add,
                                            color: kwhite,
                                          ))),
                                )
                              : Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    AnimatedContainer(
                                      duration:
                                          const Duration(milliseconds: 100),
                                      decoration: BoxDecoration(
                                          color: kblack,
                                          borderRadius:
                                              BorderRadius.circular(2)),
                                      height: 34,
                                      width: 102.0,
                                      child: Center(
                                          child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceAround,
                                        children: [
                                          Flexible(
                                            child: InkWell(
                                              onTap: () async {
                                                await DatabaseHelper.instance
                                                    .removeUpdate(ShopingCart(
                                                  mrp: double.parse(
                                                      model.mrp.toString()),
                                                  name: model.product,
                                                  qty: 1,
                                                  productID: model.productId!,
                                                  price: double.parse(
                                                      model.price.toString()),
                                                  variantID: model.variantId!,
                                                  imageURL: model.images ?? '',
                                                  weight: double.parse(
                                                      model.weight.toString()),
                                                ));
                                                setState(() {
                                                  // update = 1;
                                                });
                                                print('remove to cart');
                                              },
                                              child: const Icon(
                                                Icons.remove_outlined,
                                                color: Colors.white,
                                              ),
                                            ),
                                          ),
                                          Flexible(
                                            child: Text(
                                              "${snapshot.data ?? ""}",
                                              style: GoogleFonts.dmSans(
                                                fontSize: 18,
                                                fontWeight: FontWeight.w700,
                                                color: Colors.white,
                                              ),
                                            ),
                                          ),
                                          Flexible(
                                            child: InkWell(
                                              onTap: () async {
                                                await DatabaseHelper.instance
                                                    .addUpdate(ShopingCart(
                                                  mrp: double.parse(
                                                      model.mrp.toString()),
                                                  name: model.product,
                                                  qty: 1,
                                                  productID: model.productId!,
                                                  price: double.parse(
                                                      model.price.toString()),
                                                  variantID: model.variantId!,
                                                  imageURL: model.images ?? '',
                                                  weight: double.parse(
                                                      model.weight.toString()),
                                                ));
                                                print("qty increased");
                                                setState(() {
                                                  // update = 1;
                                                });
                                              },
                                              child: const Icon(
                                                Icons.add,
                                                color: Colors.white,
                                              ),
                                            ),
                                          )
                                        ],
                                      )),
                                    ),
                                  ],
                                );
                        },
                      );
                    });
                  }),
                ),
              ],
            ),
          ),
        ]),
      ],
    );
  }

  _customSecondCardWidget(int index) => Row(
        children: [
          index == 0
              ? const SizedBox(
                  width: 24,
                )
              : const SizedBox(),
          Material(
            elevation: 3,
            shadowColor: Colors.black,
            child: SizedBox(
              width: 285,
              height: 260,
              child: Container(
                color: Colors.white,
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 10, right: 10),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Image.asset("assets/images/combo-badge.png"),
                          getSvgIcon("assets/svg/heart-icon.svg")
                        ],
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Image.asset("assets/images/apple.png"),
                          const Icon(
                            Icons.add,
                            size: 40,
                            color: Colors.black,
                          ),
                          Image.asset("assets/images/apple.png"),
                        ],
                      ),
                      const SizedBox(height: 2),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 15),
                        child: Align(
                          alignment: Alignment.topLeft,
                          child: Row(
                            children: [
                              Text(
                                "Apple",
                                style: GoogleFonts.dmSans(
                                  fontSize: 16,
                                  color: Colors.black,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              const Icon(
                                Icons.add,
                                color: Colors.black,
                              ),
                              Text(
                                "Apple",
                                style: GoogleFonts.dmSans(
                                  fontSize: 16,
                                  color: Colors.black,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 3),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 15),
                        child: Align(
                          alignment: Alignment.topLeft,
                          child: Text(
                            "1kg + 1kg",
                            style: GoogleFonts.dmSans(
                              fontSize: 13,
                              color: Colors.grey,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 3),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 15),
                        child: Align(
                          alignment: Alignment.topLeft,
                          child: Row(
                            children: [
                              Text(
                                "₹364",
                                style: GoogleFonts.dmSans(
                                  fontSize: 16,
                                  color: Colors.black,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              const SizedBox(width: 5),
                              Text(
                                "₹520",
                                style: GoogleFonts.dmSans(
                                  fontSize: 14,
                                  color: Colors.grey,
                                  fontWeight: FontWeight.w400,
                                  decoration: TextDecoration.lineThrough,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Align(
                        alignment: Alignment.bottomCenter,
                        child: Container(
                          width: 120,
                          height: 35,
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.black, width: 1.2),
                          ),
                          child: Center(
                              child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              getSvgIcon("assets/svg/add-to-cart-icon.svg"),
                              const SizedBox(width: 10),
                              Text(
                                "Add",
                                style: GoogleFonts.dmSans(
                                  fontSize: 14,
                                  color: Colors.black,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          )),
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      );

// Category Item Widget
  _categoryItemWidget(CategoryItemModel data) {
    final int randomcolor = Random().nextInt(categorycolorlist.length);

    // print(data!.toJson());

    print(" color ${data.txtcolor}");

    return ButtonAnimation(
      onpress: () {
        final CategoryController categoryController = Get.find();
        categoryController.categoryproductmodel.clear();
        categoryController.subcategorymodel.clear();

        Get.to(() => CategoryScreen(
              categoryname: data.name.toString().capitalizeFirst!,
              categoryid: data.categoryId!,
            ));
      },
      animationWidget: ClipRRect(
        borderRadius: BorderRadius.circular(6),
        child: Container(
          decoration: BoxDecoration(
              color: data.bgcolor != null
                  ? (data.bgcolor)!.toColor()
                  : const Color(0xffF2EFED)),
          child: SizedBox(
            child: Stack(
              children: [
                Align(
                  alignment: Alignment.bottomRight,
                  child: Container(
                          child: data.categoryImage == null
                              ? Image.asset('assets/images/category.png')
                              : CachedNetworkImage(
                                  placeholder: ((context, url) {
                                    return Image.asset(
                                        'assets/images/category.png');
                                  }),
                                  errorWidget: (context, url, error) =>
                                      Image.asset('assets/images/category.png'),
                                  imageUrl: data.categoryImage))
                      .paddingOnly(left: 10),
                ),
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 2, vertical: 12),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Flexible(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(
                              "${data.name.toString().capitalize}"
                                  .capitalizeFirst!,
                              textAlign: TextAlign.center,
                              style: GoogleFonts.dmSans(
                                fontSize: 15,
                                height: 1,
                                fontWeight: FontWeight.w500,
                                color: data.txtcolor != null
                                    ? (data.txtcolor)!.toColor()
                                    : const Color(0xff7A5649),
                              ),
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

// Custom searchBar
  Container _customSearchBar() => Container(
        color: Colors.transparent,
        child: Row(
          children: [
            Expanded(
              flex: 5,
              child: SizedBox(
                height: 45,
                child: TextField(
                  readOnly: true,
                  onTap: () {
                    Get.to(() => const SearchScreen(),
                        transition: Transition.downToUp);
                  },
                  decoration: InputDecoration(
                    isDense: true,
                    filled: true,
                    fillColor: Colors.transparent,
                    prefixIcon: Padding(
                      padding: const EdgeInsets.all(16),
                      child: getSvgIcon("assets/svg/search-icon.svg"),
                    ),
                    // suffixIcon: Padding(
                    //   padding: const EdgeInsets.all(15),
                    //   child: getSvgIcon("assets/svg/voice.svg"),

                    hintText: "Search for products...",
                    hintStyle: GoogleFonts.dmSans(fontSize: 15),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(2),
                      borderSide: const BorderSide(
                        color: Color(0xffC5C8CD),
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(2),
                      borderSide: const BorderSide(
                        width: 1,
                        color: Color(0xffC5C8CD),
                      ),
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(
              width: 10,
              height: 10,
            ),
            // Container(
            //   height: 45,
            //   width: 45,
            //   child: Center(
            //     child: getSvgIcon("assets/svg/settings.svg"),
            //   ),
            //   decoration: BoxDecoration(
            //     border: Border.all(
            //       color: const Color(0xffC5C8CD),
            //     ),
            //   ),
            // )
          ],
        ),
      );
}

Widget getSvgIcon(String icon) => SvgPicture.asset(
      icon,
      semanticsLabel: 'Acme Logo',
    );

extension ColorExtension on String {
  toColor() {
    print(this);
    var hexString = this;
    final buffer = StringBuffer();
    if (hexString.length == 6 || hexString.length == 7) buffer.write('ff');
    buffer.write(hexString.replaceFirst('#', ''));

    print("buffer $buffer");
    return Color(int.parse(buffer.toString(), radix: 16));
  }
}

import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:rapsap/model/homepageproducts/home_page_products/datum.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rapsap/model/BannerAds/datum.dart';
import 'package:rapsap/model/CategoryModel/data.dart';
import 'package:rapsap/services/category_service.dart';

import '../services/orderservices.dart';
import '../view/screens/cart_screen/cart_screen.dart';

// Updated HomeViewController
class HomeViewController extends GetxController {
  int _pageIndex = 0;
  RxBool favourite = false.obs;
  bool pinned = false;
  RxBool rewards = false.obs;

  bool _isCategoryLoading = false;
  RxInt selectedindex = 0.obs;
  RxString selectedfitler = "".obs;
  List<BannerItem> bannerItems = [];
  int selectedpageitem = 0;

  List<HomePageCategoryData> homepageDatalist = [];
  List<CategoryItemModel?> _categoryModel = [];

  // New properties for better state management
  bool isLoading = true;
  bool hasError = false;
  String errorMessage = '';

  @override
  void onInit() async {
    await getHomePageViews();
    super.onInit();
  }

  getHomePageViews() async {
    try {
      isLoading = true;
      hasError = false;
      errorMessage = '';
      update(['home']);

      // Load essential data first (categories and banners)
      final essentialResults = await Future.wait([
        getBannerAds(),
        getCategory(),
      ]);

      // Load optional homepage data separately to not block UI
      getHomePageData(); // Don't await this - let it load in background

      // Get config data
      try {
        var res = await OrderServices.getConfig();
        if (res != null) {
          configModel = res;
        }
      } catch (e) {
        log('Config loading failed (non-critical): $e');
      }

      // Check if essential data is loaded (categories and banners only)
      if (categoryModel.isNotEmpty && 
          categoryModel.first != null && 
          bannerItems.isNotEmpty) {
        isLoading = false;
        log('Essential home page data loaded successfully');
      } else {
        hasError = true;
        errorMessage = 'Failed to load categories or banners';
        isLoading = false;
        log('Essential data is missing - Categories: ${categoryModel.length}, Banners: ${bannerItems.length}');
      }
    } catch (e) {
      log('Error in getHomePageViews: $e');
      hasError = true;
      errorMessage = 'Something went wrong: $e';
      isLoading = false;
    } finally {
      update(['home']);
    }
  }

  List<CategoryItemModel?> get categoryModel => _categoryModel;
  set categoryModel(List<CategoryItemModel?> val) {
    _categoryModel = val;
    update(['home']);
  }

  bool get isCategoryLoading => _isCategoryLoading;
  set isCategoryLoading(bool val) {
    _isCategoryLoading = val;
    update();
  }

  final Widget bannerimage = Image.asset(
    "assets/images/banner-image.png",
    fit: BoxFit.cover,
    height: 180,
    width: double.infinity,
  );

  int get pageIndex => _pageIndex;
  set pageIndex(int val) {
    _pageIndex = val;
    update();
  }

  Future<List<CategoryItemModel?>> getCategory() async {
    try {
      final category = await CategoryService.getCategoryList();
      if (category != null && category.data != null && category.data!.isNotEmpty) {
        categoryModel = category.data!;
        log('Categories loaded: ${categoryModel.length}');
      } else {
        log('Category data is null or empty');
        categoryModel = [];
      }
    } catch (e) {
      log('Error loading categories: $e');
      categoryModel = [];
    }
    return categoryModel;
  }

  Future<List<BannerItem?>> getBannerAds() async {
    try {
      final res = await CategoryService.getBannerAds();
      log("banner=$res");
      
      if (res != null && res.data != null && res.data!.isNotEmpty) {
        bannerItems = res.data!;
        log('Banners loaded: ${bannerItems.length}');
      } else {
        log('Banner data is null or empty');
        bannerItems = [];
      }
    } catch (e) {
      log('Error loading banners: $e');
      bannerItems = [];
    }
    return bannerItems;
  }

  Future<List<HomePageCategoryData?>> getHomePageData() async {
    try {
      final res = await CategoryService.getHomePageProductsByCategory();
      if (res != null && res.data != null) {
        homepageDatalist = res.data!;
        log('Homepage data loaded: ${homepageDatalist.length}');
      } else {
        log('Homepage data is null');
        homepageDatalist = [];
      }
    } catch (e) {
      log('Error loading homepage data: $e');
      homepageDatalist = [];
    }

    if (kDebugMode) {
      print(homepageDatalist.toList());
    }
    return homepageDatalist.toList();
  }

 
}

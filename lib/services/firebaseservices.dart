import 'dart:developer';
import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';

import '../view/widgets/commons.dart';
import '../view/widgets/notification_service.dart';

/// Modern Firebase Service following 2025 best practices
/// Simplified initialization without complex retry logic that can cause channel issues
class FirebaseService {
  // Private instances
  static FirebaseRemoteConfig? _remoteConfig;
  static FirebaseDynamicLinks? _dynamicLinks; // Note: Deprecated but keeping for compatibility
  static FirebaseAnalytics? _firebaseAnalytics;

  static bool _isInitialized = false;

  // Public getters with null checks
  static FirebaseRemoteConfig? get remoteConfig => _remoteConfig;
  static FirebaseDynamicLinks? get dynamicLinks => _dynamicLinks;
  static FirebaseAnalytics? get firebaseAnalytics => _firebaseAnalytics;

  static bool get isInitialized => _isInitialized;

  /// Modern simplified Firebase initialization
  /// Removes complex retry logic that can cause channel connection issues
  static Future<bool> init() async {
    if (_isInitialized) {
      log('🔥 Firebase already initialized');
      return true;
    }

    try {
      log('🔥 Starting Firebase service initialization...');

      // Check if Firebase Core is already initialized (should be done in main.dart)
      if (Firebase.apps.isEmpty) {
        log('⚠️ Firebase Core not initialized. Please initialize Firebase Core first.');
        return false;
      }

      // Initialize Firebase service instances
      await _initializeInstances();

      // Initialize Firebase services
      await _initializeServices();

      _isInitialized = true;
      log('✅ Firebase services initialized successfully');
      return true;

    } catch (e) {
      log('❌ Firebase service initialization error: $e');
      _isInitialized = false;

      // Don't crash the app - allow it to continue without Firebase services
      return false;
    }
  }



  /// Initialize Firebase service instances with enhanced error handling
  static Future<void> _initializeInstances() async {
    try {
      // Only initialize if Firebase is properly set up
      if (Firebase.apps.isEmpty) {
        throw Exception('Firebase apps not available');
      }

      // Initialize with additional safety checks
      try {
        _firebaseAnalytics = FirebaseAnalytics.instance;
        log('Firebase Analytics initialized');
      } catch (e) {
        log('Firebase Analytics initialization failed: $e');
        _firebaseAnalytics = null;
      }

      try {
        _remoteConfig = FirebaseRemoteConfig.instance;
        log('Firebase Remote Config initialized');
      } catch (e) {
        log('Firebase Remote Config initialization failed: $e');
        _remoteConfig = null;
      }
      
      // Dynamic Links might not be available on all platforms
      try {
        _dynamicLinks = FirebaseDynamicLinks.instance;
        log('Firebase Dynamic Links initialized');
      } catch (e) {
        log('Dynamic Links not available: $e');
        _dynamicLinks = null;
      }
      
      log('Firebase instances initialization completed');
    } catch (e) {
      log('Error initializing Firebase instances: $e');
      rethrow;
    }
  }

  /// Initialize Firebase services with improved error isolation
  static Future<void> _initializeServices() async {
    try {
      // Initialize notification service only on supported platforms
      if (!kIsWeb) {
        try {
          await NotificationService().init();
          log('Notification service initialized');
        } catch (e) {
          log('Notification service initialization failed: $e');
          // Continue with other services even if notifications fail
        }
      }

      // Initialize remote config with error isolation
      try {
        await _initializeRemoteConfig();
        log('Remote config initialized');
      } catch (e) {
        log('Remote config initialization failed: $e');
        // Continue with other services
      }
      
      // Initialize crashlytics with error isolation
      try {
        _initializeCrashlytics();
        log('Crashlytics initialized');
      } catch (e) {
        log('Crashlytics initialization failed: $e');
        // Continue - crashlytics failure shouldn't stop the app
      }
      
    } catch (e) {
      log('Service initialization error: $e');
      // Don't rethrow here to avoid crashing the app
    }
  }

  /// Initialize Crashlytics with enhanced error handling
  static void _initializeCrashlytics() {
    try {
      if (!kDebugMode && !kIsWeb && _isInitialized) {
        FlutterError.onError = (errorDetails) {
          try {
            FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
          } catch (e) {
            log('Failed to record flutter error to crashlytics: $e');
          }
        };
        
        PlatformDispatcher.instance.onError = (error, stack) {
          try {
            FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
          } catch (e) {
            log('Failed to record error to crashlytics: $e');
          }
          return true;
        };
        
        log('Crashlytics error handlers set up successfully');
      }
    } catch (e) {
      log('Crashlytics setup error: $e');
    }
  }

  /// Initialize Remote Config with enhanced error handling
  static Future<void> _initializeRemoteConfig() async {
    if (!_isInitialized || _remoteConfig == null) {
      log('Firebase not initialized or remote config unavailable');
      return;
    }
    
    try {
      await _remoteConfig!.setConfigSettings(RemoteConfigSettings(
        fetchTimeout: const Duration(seconds: 10),
        minimumFetchInterval: const Duration(hours: 1),
      ));
      log('Remote config settings applied successfully');
    } catch (e) {
      log('Remote config setup error: $e');
    }
  }

  // Backwards compatibility methods with null checks
  static void crashlytics() {
    _initializeCrashlytics();
  }

  static Future<void> remoteconfig() async {
    await _initializeRemoteConfig();
  }

  /// Safe method to get analytics observer
  static List<NavigatorObserver> getAnalyticsObserver() {
    if (_isInitialized && _firebaseAnalytics != null) {
      try {
        return [FirebaseAnalyticsObserver(analytics: _firebaseAnalytics!)];
      } catch (e) {
        log('Error creating analytics observer: $e');
        return [];
      }
    }
    return [];
  }

  /// Safe method to log analytics events
  static Future<void> logEvent(String name, {Map<String, Object>? parameters}) async {
    if (_isInitialized && _firebaseAnalytics != null) {
      try {
        await _firebaseAnalytics!.logEvent(name: name, parameters: parameters);
      } catch (e) {
        log('Error logging analytics event: $e');
      }
    }
  }

  /// Safe method to set user ID
  static Future<void> setUserId(String userId) async {
    if (_isInitialized && _firebaseAnalytics != null) {
      try {
        await _firebaseAnalytics!.setUserId(id: userId);
      } catch (e) {
        log('Error setting user ID: $e');
      }
    }
  }

  /// Safe method to log app open
  static Future<void> logAppOpen() async {
    if (_isInitialized && _firebaseAnalytics != null) {
      try {
        await _firebaseAnalytics!.logAppOpen();
      } catch (e) {
        log('Error logging app open: $e');
      }
    }
  }

  /// Get dynamic link (safely)
  /// Note: Firebase Dynamic Links is deprecated and will shut down on August 25, 2025
  /// Consider migrating to alternative solutions like App Links or Universal Links
  static Future<PendingDynamicLinkData?> getInitialLink() async {
    if (_isInitialized && _dynamicLinks != null) {
      try {
        return await _dynamicLinks!.getInitialLink();
      } catch (e) {
        log('Error getting initial dynamic link: $e');
        return null;
      }
    }
    return null;
  }

  /// Handle dynamic link (safely)
  /// Note: Firebase Dynamic Links is deprecated and will shut down on August 25, 2025
  /// Consider migrating to alternative solutions like App Links or Universal Links
  static void handleDynamicLinks(Function(PendingDynamicLinkData) onLink) {
    if (_isInitialized && _dynamicLinks != null) {
      try {
        _dynamicLinks!.onLink.listen(onLink, onError: (error) {
          log('Dynamic link error: $error');
        });
      } catch (e) {
        log('Error setting up dynamic link handler: $e');
      }
    }
  }
}
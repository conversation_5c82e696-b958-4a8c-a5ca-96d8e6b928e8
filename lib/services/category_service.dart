import 'dart:convert';
import 'dart:developer';
import 'package:rapsap/model/homepageproducts/home_page_products/home_page_products.dart';
import 'package:rapsap/services/http_logging_client.dart';
import 'package:rapsap/view/widgets/commons.dart';
import 'package:http/http.dart' as http;
import 'package:rapsap/model/CategoryModel/category_model.dart';
import 'package:rapsap/model/SubcategoryModel/sub_category_model/sub_category_model.dart';

import '../main.dart';
import '../model/BannerAds/banner_ads.dart';
import '../model/category_product_model/category_products_model/category_products_model.dart';

// Updated CategoryService with better error handling
class CategoryService {
  static Future<CategoryModel?> getCategoryList() async {
    String url = '${baseURL}api/v1/ecomm/getCategory';

    Map<String, dynamic> body = {
      "key": "JWT",
      "secret": "RAPSAP",
    };
    Map<String, String> headers = {};

    try {
      // Use regular http.post instead of LoggedHttpClient to avoid stream issues
      final response = await http
          .post(
            Uri.parse(url),
            body: body,
            headers: headers,
          )
          .timeout(Duration(seconds: 30));

      log('Category API status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseBody = response.body;
        log('Category API Response length: ${responseBody.length}');

        if (responseBody.isNotEmpty) {
          final responseModel = categoryModelFromJson(responseBody);
          if (responseModel.success == true && responseModel.data != null) {
            log('Category API success with ${responseModel.data!.length} items');
            return responseModel;
          } else {
            log('Category API returned success=false or null data');
          }
        }
      } else {
        log('Category API failed with status: ${response.statusCode}');
      }
      return null;
    } catch (e) {
      log("CategoryService error: $e");
      return null;
    }
  }

  static Future<SubCategoryModel?> getSUbCategoryList({categoryId}) async {
    String url = '${baseURL}api/v1/ecomm/getSubcategory';

    Map<String, dynamic> body = {
      "key": "JWT",
      "secret": "RAPSAP",
      "category_id": categoryId.toString()
    };
    Map<String, String> headers = {};

    try {
      final response = await http
          .post(
            Uri.parse(url),
            body: body,
            headers: headers,
          )
          .timeout(Duration(seconds: 30));

      log("Subcategory status: ${response.statusCode}");

      if (response.statusCode == 200) {
        log(response.body.toString());
        final responseModel = SubCategoryModel.fromJson(response.body);
        return responseModel;
      }
      return null;
    } catch (e) {
      log("Subcategory error: $e");
    }
    return null;
  }

  static Future<BannerAds?> getBannerAds() async {
    String url = '${baseURL}api/v1/ecomm/getBanner';

    GetStorage storage = GetStorage();
    String jwt = storage.read('JWT') ?? '';

    Map<String, String> headers = {
      'Authorization': 'Bearer $jwt',
      'Content-type': 'application/json'
    };
    var client = http.Client();

    try {
      var response = await client
          .post(Uri.parse(url), body: json.encode({}), headers: headers)
          .timeout(Duration(seconds: 30)); // Add timeout

      log("Banner API status: ${response.statusCode}");

      if (response.statusCode == 200) {
        var jsonRes = response.body;
        log('Banner API Response length: ${jsonRes.length}');

        if (jsonRes.isNotEmpty) {
          var jsonResponse = json.decode(jsonRes);

          if (jsonResponse['success'] == false) {
            log('Banner API returned success=false');
            return null;
          } else {
            log('Banner data count: ${jsonResponse['data']?.length ?? 0}');
            BannerAds bannerAds = BannerAds.fromJson(jsonResponse);
            return bannerAds;
          }
        }
      } else {
        log('Banner API failed with status: ${response.statusCode}');
      }
    } catch (e, stackTrace) {
      log('Error in getBannerAds: $e\nStackTrace: $stackTrace');
    } finally {
      client.close();
    }
    return null;
  }

  static Future<CategoryProductModel?> getPrioductsyoumaylike() async {
    String url = "${baseURL}api/v1/ecomm/productsMayLikes";
    String jwt = storage.read('JWT') ?? '';

    Map<String, String> headers = {
      'Authorization': 'Bearer $jwt',
    };
    Map<String, dynamic> body = {"store_id": "1"};

    try {
      final response = await http
          .post(Uri.parse(url), body: body, headers: headers)
          .timeout(Duration(seconds: 30));

      if (response.statusCode == 200) {
        final responseModel = CategoryProductModel.fromJson(response.body);
        log(responseModel.data.toString());
        return responseModel;
      }
      return null;
    } catch (e) {
      log("Products you may like error: $e");
    }
    return null;
  }

  static Future<HomePageProducts?> getHomePageProductsByCategory() async {
    String url = "${baseURL}api/v1/ecomm/getFiveProductsPerCategory";

    try {
      final UserController controller = Get.find<UserController>();

      Map<String, dynamic> body = {};
      log('StoreID: ${storage.read('storeID')}');

      if (controller.userdata.value.data == null) {
        body = {
          "key": "JWT",
          "store_id": (storage.read('storeID') ?? 0).toString(),
          "secret": "RAPSAP"
        };
      } else {
        body = {
          "key": "JWT",
          "store_id": (storage.read('storeID') ?? 0).toString(),
          "secret": "RAPSAP",
          "user_id": controller.userdata.value.data!.id.toString()
        };
      }

      final response = await http
          .post(
            Uri.parse(url),
            body: body,
          )
          .timeout(Duration(seconds: 15)); // Increase timeout slightly

      log('Homepage products API status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseModel = HomePageProducts.fromJson(response.body);
        log('Homepage products loaded: ${responseModel.data?.length ?? 0}');
        return responseModel;
      } else {
        log('Homepage products API failed with status: ${response.statusCode}');
      }
      return null;
    } catch (e) {
      // Check if it's a timeout specifically
      if (e.toString().contains('TimeoutException')) {
        log("Homepage products timed out - this is okay, UI will still work");
      } else {
        log("Homepage products error: $e");
      }
      // Return null but don't block the UI
      return null;
    }
  }

  // Keep the existing getProductsByCategory method as is
  static Future<CategoryProductModel?> getProductsByCategory(
      {required int categoryId,
      String? type,
      int page = 0,
      String? subcategoryid,
      String? filter}) async {
    String url = "${baseURL}api/v1/ecomm/getProductsByCategory";
    Map<String, dynamic> body = {};
    final UserController controller = Get.find<UserController>();

    // Your existing logic for building the body...
    if (filter != "" && filter != null) {
      if (type != "sub_category") {
        if (controller.userdata.value.data != null) {
          body = {
            "category_id": categoryId.toString(),
            "type": "category",
            "store_id": (storage.read('storeID') ?? 0).toString(),
            "user_id": controller.userdata.value.data!.id.toString(),
            "page": page.toString(),
            "key": "JWT",
            "secret": "RAPSAP",
            "filter": filter
          };
        } else {
          body = {
            "category_id": categoryId.toString(),
            "type": "category",
            "store_id": (storage.read('storeID') ?? 0).toString(),
            "page": page.toString(),
            "key": "JWT",
            "secret": "RAPSAP",
            "filter": filter
          };
        }
      } else {
        if (controller.userdata.value.data != null) {
          body = {
            "category_id": categoryId.toString(),
            "type": "sub_category",
            "store_id": (storage.read('storeID') ?? 0).toString(),
            "user_id": controller.userdata.value.data!.id.toString(),
            "page": page.toString(),
            "sub_category_id": subcategoryid,
            "key": "JWT",
            "secret": "RAPSAP",
          };
        } else {
          body = {
            "category_id": categoryId.toString(),
            "type": "sub_category",
            "store_id": (storage.read('storeID') ?? 0).toString(),
            "page": page.toString(),
            "sub_category_id": subcategoryid,
            "key": "JWT",
            "secret": "RAPSAP",
            "filter": filter
          };
        }
      }
    } else {
      if (type == "home") {
        if (controller.userdata.value.data != null) {
          body = {
            "category_id": categoryId.toString(),
            "type": "category",
            "store_id": (storage.read('storeID') ?? 0).toString(),
            "page": page.toString(),
            "user_id": controller.userdata.value.data!.id.toString(),
            "size": "10",
            "key": "JWT",
            "secret": "RAPSAP"
          };
        } else {
          body = {
            "category_id": categoryId.toString(),
            "type": "category",
            "store_id": (storage.read('storeID') ?? 0).toString(),
            "page": page.toString(),
            "size": "10",
            "key": "JWT",
            "secret": "RAPSAP"
          };
        }
      } else if (type != "sub_category") {
        if (controller.userdata.value.data != null) {
          body = {
            "category_id": categoryId.toString(),
            "type": "category",
            "store_id": (storage.read('storeID') ?? 0).toString(),
            "user_id": controller.userdata.value.data!.id.toString(),
            "page": page.toString(),
            "key": "JWT",
            "secret": "RAPSAP"
          };
        } else {
          body = {
            "category_id": categoryId.toString(),
            "type": "category",
            "store_id": (storage.read('storeID') ?? 0).toString(),
            "page": page.toString(),
            "key": "JWT",
            "secret": "RAPSAP"
          };
        }
      } else {
        body = {
          "category_id": categoryId.toString(),
          "type": "sub_category",
          "store_id": (storage.read('storeID') ?? 0).toString(),
          "page": page.toString(),
          "sub_category_id": subcategoryid,
          "key": "JWT",
          "secret": "RAPSAP"
        };
      }
    }

    try {
      final response = await http
          .post(
            Uri.parse(url),
            body: body,
          )
          .timeout(Duration(seconds: 30));

      if (response.statusCode == 200) {
        final responseModel = CategoryProductModel.fromJson(response.body);
        log("Products by category loaded: ${responseModel.data?.length ?? 0}");
        return responseModel;
      }
      return null;
    } catch (e) {
      log("getProductsByCategory error: $e");
    }
    return null;
  }
}

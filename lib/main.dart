import 'dart:developer';
import 'dart:ui';
import 'package:flutter/foundation.dart' show kDebugMode, kIsWeb;
import 'package:flutter/services.dart';
import 'package:flutter_displaymode/flutter_displaymode.dart';
import 'package:rapsap/firebase_options.dart';
import 'package:rapsap/services/logging_service.dart';
import 'package:rapsap/services/logging_config.dart';
import 'package:rapsap/services/navigation_logger.dart';
import 'package:rapsap/view/widgets/keyboardhider.dart';
import 'package:rapsap/view/widgets/notification_service.dart';
import 'view/widgets/commons.dart';

void main() async {
  // 1️⃣ Ensure Flutter engine & platform channels are ready
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // 2️⃣ Initialize Firebase with 2025 best practices
    await _initializeFirebase();

    // 3️⃣ Logging setup
    await LoggingService.instance.init();
    await LoggingConfig.instance.init();

    // 4️⃣ Local storage
    await GetStorage.init();
    await GetStorage.init('payload');

    // 5️⃣ High refresh rate (Android only)
    if (!kIsWeb) {
      try {
        await FlutterDisplayMode.setHighRefreshRate();
      } catch (e) {
        log('Display mode not supported: $e');
      }
    }

    // 6️⃣ Dependency bindings
    HomeBinding().dependencies();

    // 7️⃣ Lock orientation
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]);

    // 8️⃣ Run the app
    runApp(const Rapsap());
  } catch (e, stackTrace) {
    log('❌ Error during app initialization: $e');
    log('Stack trace: $stackTrace');

    // Still try to run the app in case of errors
    runApp(const Rapsap());
  }
}

/// Modern Firebase initialization following 2025 best practices
/// Addresses channel connection issues and ensures proper async initialization
Future<void> _initializeFirebase() async {
  try {
    log('🔥 Initializing Firebase...');

    // Initialize Firebase with proper error handling
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );

    log('✅ Firebase Core initialized successfully');

    // Initialize Crashlytics for production builds
    if (!kDebugMode && !kIsWeb) {
      await _initializeCrashlytics();
    }

    // Initialize other Firebase services
    await _initializeFirebaseServices();

    log('🎉 All Firebase services initialized successfully');
  } catch (e, stackTrace) {
    log('❌ Firebase initialization failed: $e');
    log('Stack trace: $stackTrace');

    // Don't rethrow - allow app to continue without Firebase
    // This prevents the app from crashing if Firebase has issues
  }
}

/// Initialize Firebase Crashlytics with proper error handling
Future<void> _initializeCrashlytics() async {
  try {
    // Pass all uncaught "fatal" errors from the framework to Crashlytics
    FlutterError.onError = (errorDetails) {
      FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
    };

    // Pass all uncaught asynchronous errors that aren't handled by the Flutter framework to Crashlytics
    PlatformDispatcher.instance.onError = (error, stack) {
      FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
      return true;
    };

    log('📊 Crashlytics initialized successfully');
  } catch (e) {
    log('⚠️ Crashlytics initialization failed: $e');
  }
}

/// Initialize other Firebase services with proper error isolation
Future<void> _initializeFirebaseServices() async {
  // Initialize notification service only on supported platforms
  if (!kIsWeb) {
    try {
      await NotificationService().init();
      log('📱 Notification service initialized');
    } catch (e) {
      log('⚠️ Notification service initialization failed: $e');
    }
  }
}

// Global storage
GetStorage storage = GetStorage();

class Rapsap extends StatelessWidget {
  const Rapsap({super.key});

  @override
  Widget build(BuildContext context) {
    return KeyboardHider(
      child: GetMaterialApp(
        onInit: () async {
          try {
            await NotificationService().setupInteractedMessage();
            log('📩 Notification interaction setup completed');
          } catch (e) {
            log('Error setting up notification interactions: $e');
          }
        },
        navigatorObservers: [
          FirebaseAnalyticsObserver(analytics: FirebaseAnalytics.instance),
          NavigationLogger(),
        ],
        defaultTransition: Transition.rightToLeft,
        initialBinding: HomeBinding(),
        theme: RapsapTheme.theme,
        debugShowCheckedModeBanner: false,
        home: const SplashScreen(),
      ),
    );
  }
}
